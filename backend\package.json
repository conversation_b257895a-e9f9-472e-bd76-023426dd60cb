{"name": "veo-backend", "version": "1.0.0", "description": "Backend API for Veo application", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "typescript", "api"], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.13.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "prisma": "^6.13.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.2.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}