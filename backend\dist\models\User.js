"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prisma = exports.UserStore = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
exports.prisma = prisma;
class UserStore {
    static async create(userData) {
        return await prisma.user.create({
            data: userData
        });
    }
    static async findByEmail(email) {
        return await prisma.user.findUnique({
            where: { email }
        });
    }
    static async findById(id) {
        return await prisma.user.findUnique({
            where: { id }
        });
    }
    static async emailExists(email) {
        const user = await prisma.user.findUnique({
            where: { email }
        });
        return !!user;
    }
    static toResponse(user) {
        const { password, ...userResponse } = user;
        return userResponse;
    }
}
exports.UserStore = UserStore;
//# sourceMappingURL=User.js.map