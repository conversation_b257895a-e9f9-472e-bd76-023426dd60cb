# Veo API Documentation

## Base URL
```
http://localhost:3001/api
```

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Endpoints

### Health Check

#### GET /health
Check if the API server is running.

**Response:**
```json
{
  "status": "OK",
  "timestamp": "2025-08-04T12:00:00.000Z"
}
```

### Authentication

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (201 Created):**
```json
{
  "success": true,
  "message": "User registered successfully",
  "user": {
    "id": "cmdx31n160000hhlk0kd3po59",
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "createdAt": "2025-08-04T12:00:00.000Z",
    "updatedAt": "2025-08-04T12:00:00.000Z"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Error Responses:**
- `400 Bad Request`: Missing required fields or invalid data
- `409 Conflict`: Email already exists

#### POST /auth/login
Login with existing credentials.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Login successful",
  "user": {
    "id": "cmdx31n160000hhlk0kd3po59",
    "name": "John Doe",
    "email": "<EMAIL>",
    "createdAt": "2025-08-04T12:00:00.000Z",
    "updatedAt": "2025-08-04T12:00:00.000Z"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Error Responses:**
- `400 Bad Request`: Missing email or password
- `401 Unauthorized`: Invalid credentials

#### GET /auth/me
Get current user information (requires authentication).

**Headers:**
```
Authorization: Bearer <your-jwt-token>
```

**Response (200 OK):**
```json
{
  "success": true,
  "user": {
    "id": "cmdx31n160000hhlk0kd3po59",
    "name": "John Doe",
    "email": "<EMAIL>",
    "createdAt": "2025-08-04T12:00:00.000Z",
    "updatedAt": "2025-08-04T12:00:00.000Z"
  }
}
```

**Error Responses:**
- `401 Unauthorized`: Missing or invalid token
- `404 Not Found`: User not found

## Error Handling

All errors follow a consistent format:

```json
{
  "success": false,
  "message": "Error description"
}
```

### Common HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required or failed
- `403 Forbidden`: Access denied
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource already exists
- `500 Internal Server Error`: Server error

## Rate Limiting

Currently, no rate limiting is implemented. In production, consider implementing rate limiting to prevent abuse.

## CORS

The API is configured to accept requests from:
- `http://localhost:5173` (development frontend)
- Configure additional origins in production

## Security

- Passwords are hashed using bcrypt with 12 salt rounds
- JWT tokens expire after 7 days (configurable)
- Security headers are applied using Helmet middleware
- Input validation is performed on all endpoints

## Example Usage

### JavaScript/Fetch
```javascript
// Register
const response = await fetch('http://localhost:3001/api/auth/register', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'password123'
  })
});

const data = await response.json();
```

### cURL
```bash
# Register
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"John Doe","email":"<EMAIL>","password":"password123"}'

# Login
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Get current user
curl -X GET http://localhost:3001/api/auth/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```
