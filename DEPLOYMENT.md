# Deployment Guide

This guide covers deploying the Veo application to various platforms.

## 🚀 Quick Deployment Options

### Frontend Deployment

#### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Set build command: `npm run build`
4. Set output directory: `dist`
5. Add environment variable: `VITE_API_URL=https://your-backend-url.com`

#### Netlify
1. Build the project: `npm run build`
2. Upload the `dist` folder to Netlify
3. Configure redirects for SPA routing

### Backend Deployment

#### Railway (Recommended)
1. Connect your GitHub repository
2. Set root directory to `backend`
3. Railway will auto-detect Node.js and deploy
4. Add environment variables in Railway dashboard

#### Render
1. Connect your repository
2. Set build command: `npm install && npm run build`
3. Set start command: `npm start`
4. Add environment variables

## 🔧 Environment Configuration

### Frontend Environment Variables
Create `.env` in frontend directory:
```env
VITE_API_URL=https://your-backend-url.com
```

### Backend Environment Variables
```env
NODE_ENV=production
PORT=3001
JWT_SECRET=your-super-secure-secret-key-for-production
JWT_EXPIRES_IN=7d
FRONTEND_URL=https://your-frontend-url.com
DATABASE_URL=your-production-database-url
```

## 🗄️ Database Setup

### SQLite (Development/Small Scale)
- Default configuration works out of the box
- Database file is created automatically

### PostgreSQL (Production Recommended)

1. **Update Prisma Schema**
   ```prisma
   datasource db {
     provider = "postgresql"
     url      = env("DATABASE_URL")
   }
   ```

2. **Get PostgreSQL Database**
   - Railway: Automatic PostgreSQL addon
   - Supabase: Free PostgreSQL with additional features
   - Neon: Serverless PostgreSQL

3. **Run Migrations**
   ```bash
   npx prisma migrate deploy
   ```

## 📦 Build Process

### Frontend Build
```bash
cd frontend
npm install
npm run build
```

### Backend Build
```bash
cd backend
npm install
npm run build
```

## 🔒 Production Security Checklist

- [ ] Change JWT_SECRET to a strong, random value
- [ ] Use HTTPS in production
- [ ] Set NODE_ENV=production
- [ ] Configure CORS for your domain
- [ ] Use a production database (PostgreSQL)
- [ ] Enable rate limiting
- [ ] Set up monitoring and logging
- [ ] Regular security updates

## 🌐 Custom Domain Setup

### Frontend (Vercel)
1. Add custom domain in Vercel dashboard
2. Configure DNS records as instructed
3. SSL certificate is automatically provisioned

### Backend (Railway)
1. Add custom domain in Railway dashboard
2. Configure DNS CNAME record
3. SSL certificate is automatically provisioned

## 📊 Monitoring and Logging

### Recommended Tools
- **Error Tracking**: Sentry
- **Analytics**: Google Analytics, Mixpanel
- **Uptime Monitoring**: UptimeRobot
- **Performance**: Lighthouse CI

### Backend Logging
Consider adding structured logging:
```bash
npm install winston
```

## 🔄 CI/CD Pipeline

### GitHub Actions Example
Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: cd frontend && npm install && npm run build
      
  deploy-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: cd backend && npm install && npm run build
```

## 🚨 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Update FRONTEND_URL in backend .env
   - Check CORS configuration in backend

2. **Database Connection Issues**
   - Verify DATABASE_URL format
   - Check database server status
   - Run migrations: `npx prisma migrate deploy`

3. **Build Failures**
   - Check Node.js version compatibility
   - Clear node_modules and reinstall
   - Verify all environment variables

4. **Authentication Issues**
   - Verify JWT_SECRET is set
   - Check token expiration settings
   - Ensure HTTPS in production

## 📈 Scaling Considerations

### Performance Optimization
- Enable gzip compression
- Use CDN for static assets
- Implement caching strategies
- Database connection pooling

### Horizontal Scaling
- Load balancer setup
- Database read replicas
- Redis for session storage
- Microservices architecture

## 💰 Cost Optimization

### Free Tier Options
- **Frontend**: Vercel, Netlify (generous free tiers)
- **Backend**: Railway, Render (limited free hours)
- **Database**: Supabase, PlanetScale (free tiers available)

### Paid Recommendations
- **Frontend**: Vercel Pro ($20/month)
- **Backend**: Railway Pro ($5/month)
- **Database**: Supabase Pro ($25/month)

Total estimated cost: ~$50/month for a production application with moderate traffic.
