import { PrismaClient, User } from '@prisma/client';
declare const prisma: PrismaClient<import(".prisma/client").Prisma.PrismaClientOptions, never, import("@prisma/client/runtime/library").DefaultArgs>;
export interface CreateUserData {
    name: string;
    email: string;
    password: string;
}
export interface UserResponse {
    id: string;
    name: string;
    email: string;
    createdAt: Date;
    updatedAt: Date;
}
export declare class UserStore {
    static create(userData: CreateUserData): Promise<User>;
    static findByEmail(email: string): Promise<User | null>;
    static findById(id: string): Promise<User | null>;
    static emailExists(email: string): Promise<boolean>;
    static toResponse(user: User): UserResponse;
}
export { prisma };
//# sourceMappingURL=User.d.ts.map