import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Welcome to your Dashboard, {user?.name}!
        </h1>
        <p className="text-gray-600">
          This is your personal dashboard where you can manage your account and access various features.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-3 text-blue-600">Profile</h3>
          <p className="text-gray-600 mb-4">
            Manage your personal information and account settings.
          </p>
          <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
            Edit Profile
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-3 text-green-600">Analytics</h3>
          <p className="text-gray-600 mb-4">
            View your activity statistics and performance metrics.
          </p>
          <button className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors">
            View Analytics
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-3 text-purple-600">Settings</h3>
          <p className="text-gray-600 mb-4">
            Configure your preferences and application settings.
          </p>
          <button className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 transition-colors">
            Open Settings
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-3 text-orange-600">Projects</h3>
          <p className="text-gray-600 mb-4">
            Create and manage your projects and collaborations.
          </p>
          <button className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 transition-colors">
            View Projects
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-3 text-red-600">Support</h3>
          <p className="text-gray-600 mb-4">
            Get help and contact our support team for assistance.
          </p>
          <button className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors">
            Contact Support
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold mb-3 text-indigo-600">Documentation</h3>
          <p className="text-gray-600 mb-4">
            Access guides, tutorials, and API documentation.
          </p>
          <button className="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600 transition-colors">
            Read Docs
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mt-6">
        <h2 className="text-2xl font-semibold mb-4">Recent Activity</h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2 border-b">
            <span className="text-gray-700">Account created</span>
            <span className="text-gray-500 text-sm">Just now</span>
          </div>
          <div className="flex items-center justify-between py-2 border-b">
            <span className="text-gray-700">Profile updated</span>
            <span className="text-gray-500 text-sm">2 minutes ago</span>
          </div>
          <div className="flex items-center justify-between py-2">
            <span className="text-gray-700">Welcome email sent</span>
            <span className="text-gray-500 text-sm">5 minutes ago</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
