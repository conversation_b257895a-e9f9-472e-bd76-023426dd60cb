"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrentUser = exports.login = exports.register = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const User_1 = require("../models/User");
const jwt_1 = require("../utils/jwt");
const errorHandler_1 = require("../middleware/errorHandler");
const register = async (req, res, next) => {
    try {
        const { name, email, password } = req.body;
        // Validation
        if (!name || !email || !password) {
            return next((0, errorHandler_1.createError)('Name, email, and password are required', 400));
        }
        if (password.length < 6) {
            return next((0, errorHandler_1.createError)('Password must be at least 6 characters long', 400));
        }
        // Check if user already exists
        if (await User_1.UserStore.emailExists(email)) {
            return next((0, errorHandler_1.createError)('User with this email already exists', 409));
        }
        // Hash password
        const saltRounds = 12;
        const hashedPassword = await bcryptjs_1.default.hash(password, saltRounds);
        // Create user
        const userData = {
            name,
            email,
            password: hashedPassword
        };
        const user = await User_1.UserStore.create(userData);
        const userResponse = User_1.UserStore.toResponse(user);
        // Generate token
        const token = (0, jwt_1.generateToken)({
            id: user.id,
            email: user.email,
            name: user.name
        });
        res.status(201).json({
            success: true,
            message: 'User registered successfully',
            user: userResponse,
            token
        });
    }
    catch (error) {
        next(error);
    }
};
exports.register = register;
const login = async (req, res, next) => {
    try {
        const { email, password } = req.body;
        // Validation
        if (!email || !password) {
            return next((0, errorHandler_1.createError)('Email and password are required', 400));
        }
        // Find user
        const user = await User_1.UserStore.findByEmail(email);
        if (!user) {
            return next((0, errorHandler_1.createError)('Invalid email or password', 401));
        }
        // Check password
        const isPasswordValid = await bcryptjs_1.default.compare(password, user.password);
        if (!isPasswordValid) {
            return next((0, errorHandler_1.createError)('Invalid email or password', 401));
        }
        // Generate token
        const token = (0, jwt_1.generateToken)({
            id: user.id,
            email: user.email,
            name: user.name
        });
        const userResponse = User_1.UserStore.toResponse(user);
        res.json({
            success: true,
            message: 'Login successful',
            user: userResponse,
            token
        });
    }
    catch (error) {
        next(error);
    }
};
exports.login = login;
const getCurrentUser = async (req, res, next) => {
    try {
        if (!req.user) {
            return next((0, errorHandler_1.createError)('User not authenticated', 401));
        }
        const user = await User_1.UserStore.findById(req.user.id);
        if (!user) {
            return next((0, errorHandler_1.createError)('User not found', 404));
        }
        const userResponse = User_1.UserStore.toResponse(user);
        res.json({
            success: true,
            user: userResponse
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getCurrentUser = getCurrentUser;
//# sourceMappingURL=authController.js.map