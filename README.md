# Veo - Modern Full-Stack Web Application

A modern, full-stack web application built with React, TypeScript, Node.js, and SQLite. Features user authentication, responsive design, and a clean, modern UI.

## 🚀 Features

- **Modern Tech Stack**: React 18, TypeScript, Tailwind CSS, Node.js, Express, Prisma
- **Authentication**: JWT-based authentication with secure password hashing
- **Responsive Design**: Mobile-first design that works on all devices
- **Database**: SQLite with Prisma ORM for easy development and deployment
- **Type Safety**: Full TypeScript support across frontend and backend
- **Modern UI**: Clean, professional interface with Tailwind CSS
- **Hot Reload**: Development servers with hot reload for fast iteration

## 📋 Prerequisites

- Node.js (v16 or higher)
- npm or yarn package manager

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd veo
   ```

2. **Install backend dependencies**
   ```bash
   cd backend
   npm install
   ```

3. **Install frontend dependencies**
   ```bash
   cd ../frontend
   npm install
   ```

4. **Set up the database**
   ```bash
   cd ../backend
   npx prisma migrate dev --name init
   ```

## 🚀 Running the Application

### Development Mode

1. **Start the backend server**
   ```bash
   cd backend
   npm run dev
   ```
   The backend will run on `http://localhost:3001`

2. **Start the frontend development server**
   ```bash
   cd frontend
   npm run dev
   ```
   The frontend will run on `http://localhost:5173`

### Production Mode

1. **Build the backend**
   ```bash
   cd backend
   npm run build
   npm start
   ```

2. **Build the frontend**
   ```bash
   cd frontend
   npm run build
   npm run preview
   ```

## 📁 Project Structure

```
veo/
├── backend/                 # Node.js/Express backend
│   ├── src/
│   │   ├── controllers/     # Route controllers
│   │   ├── middleware/      # Express middleware
│   │   ├── models/          # Data models
│   │   ├── routes/          # API routes
│   │   ├── utils/           # Utility functions
│   │   └── index.ts         # Main server file
│   ├── prisma/              # Database schema and migrations
│   └── package.json
├── frontend/                # React frontend
│   ├── src/
│   │   ├── components/      # Reusable components
│   │   ├── contexts/        # React contexts
│   │   ├── pages/           # Page components
│   │   ├── services/        # API services
│   │   └── types/           # TypeScript types
│   └── package.json
└── README.md
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user (protected)

### Health Check
- `GET /api/health` - Server health check

## 🎨 UI Components

- **Home Page**: Landing page with feature highlights
- **Authentication**: Login and registration forms
- **Dashboard**: Protected user dashboard
- **Navigation**: Responsive navigation bar
- **Loading States**: Elegant loading spinners and states

## 🔒 Security Features

- Password hashing with bcrypt
- JWT token authentication
- Protected routes
- CORS configuration
- Security headers with Helmet
- Input validation

## 🛠️ Technologies Used

### Frontend
- React 18
- TypeScript
- Tailwind CSS
- React Router
- Axios
- Vite

### Backend
- Node.js
- Express.js
- TypeScript
- Prisma ORM
- SQLite
- JWT
- bcrypt
- Helmet
- CORS

## 📝 Environment Variables

Create a `.env` file in the backend directory:

```env
NODE_ENV=development
PORT=3001
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d
FRONTEND_URL=http://localhost:5173
DATABASE_URL="file:./dev.db"
```

## 🚀 Deployment

The application is ready for deployment to platforms like:
- **Frontend**: Vercel, Netlify, or any static hosting
- **Backend**: Railway, Render, Heroku, or any Node.js hosting
- **Database**: Can be upgraded to PostgreSQL for production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.

## 🆘 Support

If you encounter any issues or have questions, please open an issue on the repository.
