"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const errorHandler_1 = require("./errorHandler");
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        return next((0, errorHandler_1.createError)('Access token required', 401));
    }
    const secret = process.env.JWT_SECRET || 'your-secret-key';
    jsonwebtoken_1.default.verify(token, secret, (err, decoded) => {
        if (err) {
            return next((0, errorHandler_1.createError)('Invalid or expired token', 403));
        }
        req.user = decoded;
        next();
    });
};
exports.authenticateToken = authenticateToken;
//# sourceMappingURL=auth.js.map