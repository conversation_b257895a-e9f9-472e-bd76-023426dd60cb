import { Request, Response, NextFunction } from 'express';
import bcrypt from 'bcryptjs';
import { UserStore, CreateUserData } from '../models/User';
import { generateToken } from '../utils/jwt';
import { createError } from '../middleware/errorHandler';
import { AuthRequest } from '../middleware/auth';

export const register = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { name, email, password } = req.body;

    // Validation
    if (!name || !email || !password) {
      return next(createError('Name, email, and password are required', 400));
    }

    if (password.length < 6) {
      return next(createError('Password must be at least 6 characters long', 400));
    }

    // Check if user already exists
    if (await UserStore.emailExists(email)) {
      return next(createError('User with this email already exists', 409));
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user
    const userData: CreateUserData = {
      name,
      email,
      password: hashedPassword
    };

    const user = await UserStore.create(userData);
    const userResponse = UserStore.toResponse(user);

    // Generate token
    const token = generateToken({
      id: user.id,
      email: user.email,
      name: user.name
    });

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      user: userResponse,
      token
    });
  } catch (error) {
    next(error);
  }
};

export const login = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { email, password } = req.body;

    // Validation
    if (!email || !password) {
      return next(createError('Email and password are required', 400));
    }

    // Find user
    const user = await UserStore.findByEmail(email);
    if (!user) {
      return next(createError('Invalid email or password', 401));
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return next(createError('Invalid email or password', 401));
    }

    // Generate token
    const token = generateToken({
      id: user.id,
      email: user.email,
      name: user.name
    });

    const userResponse = UserStore.toResponse(user);

    res.json({
      success: true,
      message: 'Login successful',
      user: userResponse,
      token
    });
  } catch (error) {
    next(error);
  }
};

export const getCurrentUser = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return next(createError('User not authenticated', 401));
    }

    const user = await UserStore.findById(req.user.id);
    if (!user) {
      return next(createError('User not found', 404));
    }

    const userResponse = UserStore.toResponse(user);

    res.json({
      success: true,
      user: userResponse
    });
  } catch (error) {
    next(error);
  }
};
